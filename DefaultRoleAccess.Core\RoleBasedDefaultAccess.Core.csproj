<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Serilog" Version="4.3.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\FA_Libraries\Library.StorageWriter\Library.StorageWriter.csproj" />
    <ProjectReference Include="..\FA_Libraries\Library.StringHelpers\Library.StringHelpers.csproj" />
    <ProjectReference Include="..\NewDashboardWebjobs.DbStorage\NewDashboardWebjobs.DbStorage.csproj" />
  </ItemGroup>

</Project>
