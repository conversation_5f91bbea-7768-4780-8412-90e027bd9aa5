﻿using Azure.Identity;
using Microsoft.Extensions.Configuration;

namespace WebJobs.Tests.Configurations
{

    public static class Configuration
    {
        public static IConfiguration GetConfiguration()
        {
            var env = Environment.GetEnvironmentVariable("BuildEnvironment");
            var configBuilder = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", true, true)
                .AddJsonFile($"appsettings.{env}.json", true, true)
                .AddEnvironmentVariables();
            var keyVaultEndpoint = Environment.GetEnvironmentVariable("KEYVAULT_ENDPOINT");
            if (!string.IsNullOrEmpty(keyVaultEndpoint))
            {
                configBuilder.AddAzureKeyVault(new Uri(keyVaultEndpoint), new DefaultAzureCredential());
            }

            return configBuilder.Build();
        }
    }
}
