﻿
namespace RoleBasedDefaultAccess.Core
{
    public class PermissionData
    {
        public Dictionary<string, Dictionary<string, int>> CompanyAdminPermissionList { get; set; }
        public Dictionary<string, Dictionary<string, int>> ManagerPermissionList { get; set; }
        public Dictionary<string, Dictionary<string, int>> CompanyExecutivePermissionList { get; set; }
        public Dictionary<string, Dictionary<string, int>> AccountManagerPermissionList { get; set; }

    }

}
