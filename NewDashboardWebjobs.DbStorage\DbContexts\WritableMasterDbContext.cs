﻿using Microsoft.EntityFrameworkCore;
using NewDashboardWebjobs.DbStorage.MasterDbModels;

namespace NewDashboardWebjobs.DbStorage.DbContexts
{
    public class WritableMasterDbContext : DbContext
    {
        public WritableMasterDbContext(DbContextOptions<WritableMasterDbContext> options) : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.EnableDetailedErrors();
        }


        public DbSet<ClientEmployee> ClientEmployees { get; set; }
        public DbSet<IdsLogins> IdsLogins { get; set; }
        public DbSet<Company> Companies { get; set; }
        public DbSet<CompanyAdmin> CompanyAdmins { get; set; }
        public DbSet<RoleMaster> RoleMasters { get; set; }
        public DbSet<ReportSubscription> ReportSubscriptions { get; set; }
        public DbSet<RoleBasedReportSubscription> RoleBasedReportSubscriptions { get; set; }
    }
}
