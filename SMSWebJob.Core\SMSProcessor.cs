﻿using Flurl;
using Flurl.Http;
using Library.SlackService;
using Library.SMSHelpers;
using Microsoft.Extensions.Options;
using Serilog;
using SMSWebJob.Core.Helper;
using SMSWebJob.Core.Model;
using System.Text.Json;

namespace SMSWebJob.Core;

public class SMSProcessor(ErrorMessenger errorMessenger, IOptions<SMSOptions> options)
{
    private readonly SMSOptions _options = options.Value;

    private async Task SendSMS(SMSMessage smsMessage, bool isDevDeployment)
    {
        if (isDevDeployment)
        {
            await errorMessenger.SendToSlackWithAttachments($"SMS to:{smsMessage.To}", smsMessage.Message, "debugrouted");
            return;
        }
        await Execute(smsMessage);
    }

    public async Task Process(SMSMessage smsMessage, bool isDevDeployment)
    {
        try
        {
            await SendSMS(smsMessage, isDevDeployment);
            Log.Information("Sent message to: {To}, for subject: \n{Message}", smsMessage.To, smsMessage);
        }
        catch (Exception ex)
        {
            throw new SmsProcessingException($"Failed Sending SMS to: {smsMessage.To}", ex);
        }
    }

    private async Task Execute(SMSMessage smsMessage)
    {
        try
        {
            var smsURL = $"https://api.exotel.com/v1/Accounts/{_options.CallSid}/Sms/send.json";

            var response = await smsURL
                .WithBasicAuth(_options.CallSid, _options.Token)
                .AllowAnyHttpStatus()
                .PostUrlEncodedAsync(new
                {
                    From = _options.From,
                    To = smsMessage.To,
                    Body = smsMessage.Message
                });

            var responseString = await response.ResponseMessage.Content.ReadAsStringAsync();

            if (response.StatusCode >= 300)
            {
                // Fallback to VFirst SMS, return its response string
                await SendVFirstMessageAsync(smsMessage.To, smsMessage.Message);
            }
            else
            {
                if (!string.IsNullOrEmpty(responseString))
                {
                    var result = JsonSerializer.Deserialize<SMSMessageBody>(responseString);
                    if ((result?.SMSMessage?.DetailedStatusCode ?? 0) >= 23080)
                    {
                        await SendTwilioMessageAsync(smsMessage.To, smsMessage.Message);
                        return;
                    }
                    if ((result?.SMSMessage?.SmsStatusEnum(result.SMSMessage.Status) ?? SmsStatus.FailedDnd) >= SmsStatus.FailedDnd)
                    {
                        Log.Error("Failed To Send SMS: {Response}", response);
                    }
                    Log.Warning(responseString);
                }
            }
        }
        catch (FlurlHttpException)
        {
            await SendVFirstMessageAsync(smsMessage.To, smsMessage.Message);
        }
    }

    private async Task SendVFirstMessageAsync(string toNumber, string messageBody)
    {
        string username = "fli2knwhttptrn"; // Twilio Account SID
        string password = "fli2knw@(01"; // Twilio Auth Token (keep this secure and not hardcoded in production)
        string from = "FLDAST"; // Sender phone number

        try
        {
            var response = await "https://http.myvfirst.com/smpp/sendsms"
                .SetQueryParams(new
                {
                    username,
                    password,
                    to = toNumber,
                    from,
                    text = messageBody
                })
                .AllowAnyHttpStatus()
                .GetAsync();

            var responseContent = await response.GetStringAsync();

            if (response.StatusCode < 300)
            {
                Log.Warning($"To:{toNumber} with response:{responseContent}");
            }
            else
            {
                // Fallback to Twilio SMS and return its response string
                await SendTwilioMessageAsync(toNumber, messageBody);
            }
        }
        catch (Exception ex)
        {
            await SendTwilioMessageAsync(toNumber, messageBody);
            // Return exception message as response for caller to handle
            Log.Error($"Error in SendVFirstMessageAsync: {ex.Message}");
        }
    }

    private async Task SendTwilioMessageAsync(string toNumber, string messageBody)
    {
        string accountSid = "**********************************"; // Twilio Account SID
        string authToken = "03821979ecb7af71c2a12b216bbc2638"; // Twilio Auth Token (keep this secure and not hardcoded in production)
        string fromNumber = "***********"; // Sender phone number

        try
        {
            var response = await $"https://api.twilio.com/2010-04-01/Accounts/{accountSid}/Messages.json"
                .WithBasicAuth(accountSid, authToken)
                .AllowAnyHttpStatus()
                .PostUrlEncodedAsync(new
                {
                    From = fromNumber,
                    To = "91" + toNumber,
                    Body = messageBody
                });

            var responseContent = await response.GetStringAsync();

            if (response.StatusCode < 300)
            {
                Log.Warning($"To:{toNumber} with response:{responseContent}");
            }
            else
            {
                Log.Error($"Failed to send Twilio message. Status code: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            Log.Error($"Error in SendTwilioMessageAsync: {ex.Message}");
        }
    }
}