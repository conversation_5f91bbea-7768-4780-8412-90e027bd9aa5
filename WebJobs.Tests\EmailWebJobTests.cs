using EmailWebJob.Core;
using Library.EmailService.Interface;
using Microsoft.Extensions.DependencyInjection;
using WebJobs.Tests.Configurations;

namespace WebJobs.Tests
{
    [TestClass]
    public class ReportRequestTests
    {
        private ServiceProvider serviceProvider;

        [TestInitialize]
        public void Initialise()
        {
            Environment.SetEnvironmentVariable("KEYVAULT_ENDPOINT", "https://v3ManageReadOnly.vault.azure.net/");
            //Environment.SetEnvironmentVariable("KEYVAULT_ENDPOINT", "https://v3DebugWritable.vault.azure.net/");
            var configuration = Configuration.GetConfiguration();

            IServiceCollection serviceCollection = new ServiceCollection();
            EmailWebJob.Configuration.Dependencies.SetUp(serviceCollection, configuration);
            serviceProvider = serviceCollection.BuildServiceProvider();
        }

        [TestMethod]
        public async Task SparkEmailTest()
        {
            try
            {
                var reportGenerator = serviceProvider.GetRequiredService<IEmailHandler>();
                var email = new EmailMessage
                {
                    Subject = "Testing",
                    Message = "Hello World!",
                    FromEmail = "<EMAIL>",
                    FromName = "Field Assist",
                    To = "<EMAIL>"
                };
                await reportGenerator.SendEmailAsync(email);
            }
            catch (Exception ex)
            {
            }
        }

        [TestMethod]
        public async Task EmailTest()
        {
            try
            {
                var reportGenerator = serviceProvider.GetRequiredService<EmailProcessor>();
                var email = new EmailMessage
                {
                    Subject = "Testing",
                    Message = "Hello World!",
                    FromEmail = "<EMAIL>",
                    FromName = "Field Assist",
                    To = "<EMAIL>",
                    ContentPath = "https://raw.githubusercontent.com/toptal/haste-server/refs/heads/master/Dockerfile"
                };
                await reportGenerator.Process(email, false);
            }
            catch (Exception ex)
            {
            }
        }
    }
}