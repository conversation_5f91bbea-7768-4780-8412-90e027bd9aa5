﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.5" />
	<PackageReference Include="Microsoft.Azure.WebJobs" Version="3.0.41" />
	<PackageReference Include="Microsoft.Azure.WebJobs.Extensions.Storage" Version="5.3.4" />
	<PackageReference Include="Serilog" Version="4.3.0" />
	<PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
	<PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DefaultRoleAccess.Core\RoleBasedDefaultAccess.Core.csproj" />
  </ItemGroup>

</Project>
