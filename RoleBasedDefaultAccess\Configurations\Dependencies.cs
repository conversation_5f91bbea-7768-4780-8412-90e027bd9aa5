﻿using DefaultRoleAccess.Core;
using Library.StorageWriter.Reader_Writer;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NewDashboardWebjobs.DbStorage.DbContexts;

namespace RoleBasedDefaultAccess.Configurations
{
    public class Dependencies
    {
        public static void SqlResiliencyBuilder(SqlServerDbContextOptionsBuilder o)
        {
            o.EnableRetryOnFailure();
            o.CommandTimeout(60);
        }

        public static void SetUp(IServiceCollection services, IConfiguration config)
        {
            var masterStorageConnectionString = config.GetConnectionString("MasterStorageConnectionString");

            services.AddDbContext<WritableMasterDbContext>(options =>
                options.UseSqlServer(config.GetConnectionString("WritableMasterDbConnectionString"), op => {
                    op.EnableRetryOnFailure(); }));

            services.AddDbContext<MasterDbContext>(options =>
              options
                  .UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking)
                  .UseSqlServer(config.GetConnectionString("MasterDbConnectionString"), op =>
                  {
                      op.EnableRetryOnFailure();
                }));

            services.AddScoped<RoleBasedDefaultAccessProcessor>();
            services.AddScoped(d => new DefaultRoleScreenPermissionsBlobReader(masterStorageConnectionString));
            services.AddScoped<IServiceProvider>(_ => services.BuildServiceProvider());
        }
    }
}
