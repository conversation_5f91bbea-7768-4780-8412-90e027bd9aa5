﻿using Microsoft.EntityFrameworkCore;
using NewDashboardWebjobs.DbStorage.MasterDbModels;

namespace NewDashboardWebjobs.DbStorage.DbContexts
{
    public class MasterDbContext : DbContext
    {
        public MasterDbContext(DbContextOptions<MasterDbContext> options) : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.EnableDetailedErrors();
        }


        public DbSet<ClientEmployee> ClientEmployees { get; set; }
        public DbSet<Company> Companies { get; set; }
        public DbSet<IdsLogins> IdsLogins { get; set; }
        public DbSet<CompanyAdmin> CompanyAdmins { get; set; }
        public DbSet<RoleMaster> RoleMasters { get; set; }
        public DbSet<ReportSubscription> ReportSubscriptions { get; set; }
        public DbSet<RoleBasedReportSubscription> RoleBasedReportSubscriptions { get; set; }
        public DbSet<Module> Modules { get; set; }
        public DbSet<ScreenList> ScreenLists { get; set; }


        [Obsolete("SaveChanges not allowed on ReadOnlyContext", true)]
        public override int SaveChanges(bool acceptAllChangesOnSuccess)
        {
            throw new InvalidOperationException("SaveChanges not allowed on ReadOnlyContext");
        }

        [Obsolete("SaveChangesAsync not allowed on ReadOnlyContext", true)]
        public override Task<int> SaveChangesAsync(bool accepAllChangesOnSuccess, CancellationToken cancellationToken = default)
        {
            throw new InvalidOperationException("SaveChanges not allowed on ReadOnlyContext");
        }
    }
}
