using Library.SMSHelpers;
using Microsoft.Extensions.DependencyInjection;
using SMSWebJob.Core;
using WebJobs.Tests.Configurations;

namespace WebJobs.Tests
{
    [TestClass]
    public class SMSTests
    {
        private ServiceProvider serviceProvider;

        [TestInitialize]
        public void Initialise()
        {
            Environment.SetEnvironmentVariable("KEYVAULT_ENDPOINT", "https://v3ManageReadOnly.vault.azure.net/");
            //Environment.SetEnvironmentVariable("KEYVAULT_ENDPOINT", "https://v3DebugWritable.vault.azure.net/");
            var configuration = Configuration.GetConfiguration();

            IServiceCollection serviceCollection = new ServiceCollection();
            SMSWebjob.Configuration.Dependencies.SetUp(serviceCollection, configuration);
            serviceProvider = serviceCollection.BuildServiceProvider();
        }

        [TestMethod]
        public async Task SMSTest()
        {
            try
            {
                var reportGenerator = serviceProvider.GetRequiredService<SMSProcessor>();
                var smsMessage = new SMSMessage
                {
                    Message = "As per your request your Activation Code is 9700. Use it to activate the Manager Application. Thank you. -FieldAssist",
                    To = "**********"
                };
                await reportGenerator.Process(smsMessage, false);
            }
            catch (Exception ex)
            {
            }
        }
    }
}