﻿using Library.SlackService;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using SMSWebJob.Core;
using SMSWebJob.Core.Model;

namespace SMSWebjob.Configuration;

public static class Dependencies
{
    public const string SMSQueue = "sms-queue";
    public static string Deployment = "dev";

    public static void SetUp(IServiceCollection services, IConfiguration config)
    {
        Deployment = config["AppSettings:Deployment"] ?? "dev";

        #region Connection String Initialized

        var masterStorageConnectionString = config.GetConnectionString("MasterStorageConnectionString");

        #endregion Connection String Initialized

        #region SMS Configurations

        var smsOptions = config.GetSection("SMS").Get<SMSOptions>() ?? new();

        #endregion SMS Configurations

        services
            .AddSingleton(Options.Create(smsOptions))
            .AddSingleton(_ => new ErrorMessenger(masterStorageConnectionString, $"{Deployment}-Email Logger", "#DebugSMSRequest"))
            .AddSingleton<SMSProcessor>();
    }
}