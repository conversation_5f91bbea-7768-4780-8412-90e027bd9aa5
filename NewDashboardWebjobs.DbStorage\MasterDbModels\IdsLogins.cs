﻿using Libraries.CommonEnums;
using System.ComponentModel.DataAnnotations.Schema;

namespace NewDashboardWebjobs.DbStorage.MasterDbModels
{
    [Table("IDSLogins")]
    public class IdsLogins
    {
        public long CompanyId { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreationContext { get; set; }
        public string EmailId { get; set; }
        public long Id { get; set; }
        public bool IsDeactive { get; set; }

        public bool IsManager =>
            UserRole == PortalUserRole.AreaSalesManager ||
            UserRole == PortalUserRole.RegionalSalesManager ||
            UserRole == PortalUserRole.ZonalSalesManager ||
            UserRole == PortalUserRole.NationalSalesManager ||
            UserRole == PortalUserRole.GlobalSalesManager;

        public DateTime LastUpdatedAt { get; set; }
        public long LocalId { get; set; }
        public Guid LoginGuid { get; set; }
        public string Name { get; set; }
        public string PhoneNo { get; set; }
        public PortalUserRole UserRole { get; set; }
        public string RoleIds { get; set; }
    }
}
