﻿using Libraries.CommonEnums;

namespace NewDashboardWebjobs.DbStorage.MasterDbModels
{
    public class RoleMaster
    {
        public RoleMaster(long companyId)
        {   
            CompanyId = companyId;
            IsDeleted = false;
            CreatedAt = DateTime.UtcNow;
            LastUpdatedAt = DateTime.UtcNow;
            CreationContext = "FA_Webjobs: New Dashboard";
        }
        public long Id { get; set; }
        public string RoleName { get; set; }
        public string RoleDescription { get; set; }
        public long CompanyId { get; set; }
        public string Permissions { get; set; }
        public Hierarchy Hierarchy { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreationContext { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public bool IsDeleted { get; set; }
    }

}
