﻿<Project Sdk="Microsoft.NET.Sdk;Microsoft.NET.Sdk.Publish">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Flurl.Http" Version="4.0.2" />
    <PackageReference Include="Microsoft.Azure.WebJobs" Version="3.0.41" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Extensions.Storage" Version="5.3.4" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.5" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.18.1" />
    <PackageReference Include="Serilog" Version="4.3.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.Grafana.Loki" Version="8.3.0" />
    <PackageReference Include="Serilog.Sinks.Slack" Version="2.2.3" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\EmailWebJob.Core\EmailWebJob.Core.csproj" />
    <ProjectReference Include="..\FA_Libraries\Libraries.CommonModels\Libraries.CommonModels.csproj" />
    <ProjectReference Include="..\FA_Libraries\Library.EmailService\Library.EmailService.csproj" />
    <ProjectReference Include="..\FA_Libraries\Library.ResilientHttpClient\Library.ResilientHttpClient.csproj" />
    <ProjectReference Include="..\FA_Libraries\Library.StringHelpers\Library.StringHelpers.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Settings.job">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>