﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Serilog;
using Serilog.Events;
using SMSWebjob.Configuration;

var builder = new HostBuilder()
    .ConfigureAppConfiguration(config =>
    {
        config.AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);
    })
    .ConfigureWebJobs(b =>
    {
        b.AddAzureStorageQueues(c =>
        {
            c.BatchSize = 5;
            c.MaxPollingInterval = TimeSpan.FromSeconds(8);
        });
    })
    .ConfigureServices((context, services) =>
    {
        Dependencies.SetUp(services, context.Configuration);
    })
    .UseSerilog((_, loggerConfiguration) =>
    {
        loggerConfiguration
            .Enrich.FromLogContext()
            .WriteTo.Console(LogEventLevel.Warning);
    });

var host = builder.Build();
using (host)
{
    await host.RunAsync();
}