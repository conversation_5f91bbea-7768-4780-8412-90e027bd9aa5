﻿namespace SMSWebJob.Core.Model
{
    public class SMSMessageBody
    {
        public SMSMessageRecieved SMSMessage { get; set; }
    }

    public class SMSMessageRecieved
    {
        public string Sid { get; set; }

        public string AccountSid { get; set; }

        public string From { get; set; }

        public string To { get; set; }

        public string DateCreated { get; set; }

        public string DateUpdated { get; set; }

        public string? DateSent { get; set; }

        public string Body { get; set; }

        public string Direction { get; set; }

        public string Uri { get; set; }

        public string ApiVersion { get; set; }

        public decimal? Price { get; set; }

        public string Status { get; set; }

        public int? SmsUnits { get; set; }

        public int DetailedStatusCode { get; set; }

        public string DetailedStatus { get; set; }

        public SmsStatus SmsStatusEnum(string status)
        {
            switch (status)
            {
                case "queued":
                    return SmsStatus.Queued;

                case "sending":
                    return SmsStatus.Sending;

                case "submitted":
                    return SmsStatus.Submitted;

                case "sent":
                    return SmsStatus.Sent;

                case "failed-dnd":
                    return SmsStatus.FailedDnd;

                case "failed":
                    return SmsStatus.Failed;

                default:
                    throw new ArgumentException(status);
            }
        }
    }

    public enum SmsStatus
    {
        Queued,        // queued - SMS has been queued internally for delivery
        Sending,       // sending - SMS is in process of being sent to the upstream provider/gateway
        Submitted,     // submitted - SMS has been submitted from the system to the SMS Gateway
        Sent,          // sent - SMS was successfully delivered to the handset
        FailedDnd,     // failed-dnd - Delivery failed due to the number being in TRAI NCPR list (for promotional SMS)
        Failed         // failed - Delivery of SMS failed
    }
}