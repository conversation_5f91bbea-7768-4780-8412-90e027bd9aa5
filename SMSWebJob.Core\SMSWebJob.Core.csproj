﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\FA_Libraries\Library.EmailService\Library.EmailService.csproj" />
      <ProjectReference Include="..\FA_Libraries\Library.ResilientHttpClient\Library.ResilientHttpClient.csproj" />
      <ProjectReference Include="..\FA_Libraries\Library.SMSHelpers\Library.SMSHelpers.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Flurl.Http" Version="4.0.2" />
      <PackageReference Include="Serilog" Version="4.3.0" />
    </ItemGroup>

</Project>
