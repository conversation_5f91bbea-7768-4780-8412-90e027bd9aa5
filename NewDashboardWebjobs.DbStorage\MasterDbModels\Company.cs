﻿using Libraries.CommonEnums;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace NewDashboardWebjobs.DbStorage.MasterDbModels
{
    [Table("Companies")]
    public class Company 
    {
        [StringLength(200)]
        public string AccountManagerEmail { set; get; }

        [StringLength(200)]
        public string AccountManagerName { set; get; }

        [StringLength(8)]
        public string BeatShortName { get; set; }

        [StringLength(200)]
        public string ConfirmOrderEmailDistributor { set; get; }

        [StringLength(200)]
        public string ConfirmOrderEmailRetailer { set; get; }

        public DateTime CreatedAt { get; set; }
        public string CreationContext { get; set; }

      
        public bool Deleted { set; get; }

      
        public int? DistributorErpIncrementalNumber { get; set; }

      
        public int? DistributorErpLength { get; set; }

        [StringLength(8)]
        public string DistributorShortName { get; set; }

        [StringLength(8)]
        public string EmployeeShortName { get; set; }

        public Guid Guid { set; get; }
        public long Id { get; set; }

        [StringLength(40)]
      
        public string ImageId { get; set; }

        [StringLength(200)]
        public string KeyAccountManagerEmail { set; get; }

        [StringLength(200)]
        public string KeyAccountManagerName { set; get; }

        public DateTime LastUpdatedAt { get; set; }

        [StringLength(100)]
        public string LegalName { get; set; }

        [Required]
        [StringLength(50)]
        public string Name { get; set; }

        public long OutletIncrementalId { get; set; }

        [StringLength(40)]
        public string PreloaderBgImageId { get; set; }

        [StringLength(40)]
        public string PreloaderIcon { get; set; }

        [StringLength(200)]
        public string ProjectCoordinatorEmail { set; get; }

        [StringLength(200)]
        public string ProjectCoordinatorName { set; get; }

        [StringLength(200)]
        public string ProjectManagerEmail { set; get; }

        [StringLength(200)]
        public string ProjectManagerName { set; get; }

        public ProjectStage ProjectStage { set; get; }

        [StringLength(50)]
        public string SecretURLIdentifier { get; set; }

        [StringLength(50)]
        public string Sector { get; set; }

        [StringLength(8)]
        public string ShortName { get; set; }
        public string ModuleLists { get; set; }
    }
}
