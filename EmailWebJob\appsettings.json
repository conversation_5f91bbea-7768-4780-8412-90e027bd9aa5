﻿{
  "Logging": {
    "Debug": {
      "LogLevel": {
        "Default": "Warning"
      }
    },
    "Console": {
      "LogLevel": {
        "Default": "Warning"
      }
    }
  },
  "AzureWebJobsStorage": "DefaultEndpointsProtocol=https;AccountName=locationsnetwork;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net;",
  "AppSettings": {
    "Deployment": "prod",
    "AzureWebJobsStorage": "DefaultEndpointsProtocol=https;AccountName=locationsnetwork;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net;"
  },
  "AzureWebJobs": {
    "AzureWebJobsStorage": "DefaultEndpointsProtocol=https;AccountName=locationsnetwork;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net;"
  },
  "ConnectionStrings": {
    "MasterStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=locationsnetwork;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net;",
    "StorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=faappapiv3;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net;",
    "AzureWebJobsStorage": "DefaultEndpointsProtocol=https;AccountName=locationsnetwork;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net;",
    "TransactionDbConnectionString": "data source=ewwayxoov5.database.windows.net;initial catalog=FA_Transactions;user id=f2kAdmin;password=newPasswordiAcc2010;",
    "ReportDbConnectionString": "data source=ewwayxoov5.database.windows.net;initial catalog=FA_Reports;user id=f2kAdmin;password=newPasswordiAcc2010;",
    "ReadOnlyReportDbConnectionString": "data source=ewwayxoov5.database.windows.net;initial catalog=FA_Reports;user id=f2kAdmin;password=newPasswordiAcc2010;",
    "MasterDbConnectionString": "data source=ewwayxoov5.database.windows.net;initial catalog=Test_F2KLocationsNetwork;user id=azure_readonlyLogin;password=****************;MultipleActiveResultSets=true;",
    "RedisCacheConnectionString": "fieldassist.redis.cache.windows.net:6380,password=MMOK4o+URrY+QDG00KFEJyzH9FwxlcOuQiin0UQSwOw=,ssl=True,abortConnect=False"
  },
  "SMTPEmail": {
    "server": "smtp.sparkpostmail.com",
    "port": "587",
    "username": "SMTP_Injection",
    "password": "9a7df52c464d7bd804633d89574ffceed36f50f7",
    "enableSsl": true
  },
  "LokiUserName": "reporting",
  "LokiPassword": "bG9raSNmMmtAcmVwb3J0aW5n",
  "LokiBaseUrl": "https://loki.fieldassist.io/"
}