﻿using Libraries.CommonEnums;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System.ComponentModel.DataAnnotations.Schema;

namespace NewDashboardWebjobs.DbStorage.MasterDbModels
{
    [Table("CompanyAdmins")]
    public class CompanyAdmin
    {
        public CompanyAdmin()
        {
            //Initializing since might be used in other projetcs
            CreatedOn = DateTime.UtcNow;
        }

        [Column("Company")]
        public long CompanyId { get; set; }

        public DateTime CreatedAt { get; set; }

        [Column(TypeName = "datetime2"), Obsolete("Use Created at Instead!!")]
        public DateTime CreatedOn { get; set; }

        public string CreationContext { get; set; }
        public string EmailId { get; set; }
        public Guid Guid { get; set; }
        public long Id { get; set; }
        public bool IsBillable { set; get; }
        public bool IsDeactive { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public Guid? LoginGuid { get; set; }
        public string Name { get; set; }
        public string PhoneNo { get; set; }
        public string PositionInCompany { get; set; }
        public long? RegionalParentId { get; set; }
        public PortalUserRole? RegionaParentUserRole { get; set; }
        public PortalUserRole UserRole { get; set; }
        public string UserPositionIds { get; set; }
    }
}
