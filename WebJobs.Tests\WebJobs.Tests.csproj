﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
	<PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.5" />
	<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.5" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.0" />
    <PackageReference Include="MSTest.TestAdapter" Version="3.9.1" />
    <PackageReference Include="MSTest.TestFramework" Version="3.9.1" />
    <PackageReference Include="coverlet.collector" Version="6.0.4">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\EmailWebJob.Core\EmailWebJob.Core.csproj" />
    <ProjectReference Include="..\EmailWebJob\EmailWebJob.csproj" />
    <ProjectReference Include="..\SMSWebJob.Core\SMSWebJob.Core.csproj" />
    <ProjectReference Include="..\SMSWebJob\SMSWebJob.csproj" />
  </ItemGroup>

</Project>
