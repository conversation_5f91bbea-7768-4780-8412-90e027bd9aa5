﻿using Azure.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.DependencyInjection;
using DefaultRoleAccess.Core;
using RoleBasedDefaultAccess.Configurations;
using Serilog;
using Serilog.Events;

namespace RoleBasedDefaultAccess
{
    internal class Program
    {
        private static string GetKeyVaultEndpoint() => Environment.GetEnvironmentVariable("KEYVAULT_ENDPOINT");

        private static  IHostBuilder CreateHostBuilder(string[] args)
        {
            return Host.CreateDefaultBuilder(args)
            .ConfigureAppConfiguration((_, builder) =>
            {
                var keyVaultEndpoint = GetKeyVaultEndpoint();
                if (!string.IsNullOrEmpty(keyVaultEndpoint))
                {
                    builder.AddAzureKeyVault(new Uri(keyVaultEndpoint), new DefaultAzureCredential());
                }
            }).ConfigureWebJobs()
             .UseSerilog((_, loggerConfiguration) =>
             {
                 loggerConfiguration
                     .Enrich.FromLogContext()
                     .WriteTo.Console(LogEventLevel.Information);
             })
            .ConfigureServices((_, services) =>
            {
                Dependencies.SetUp(services, _.Configuration);
                services.AddScoped<RoleBasedDefaultAccessProcessor>();
            });
        }
            
        public static async Task Main(string[] args)
        {

            var _host = CreateHostBuilder(args)
               .Build();

            var app = _host.Services.GetRequiredService<RoleBasedDefaultAccessProcessor>();

#if !DEBUG
            var companyIds = args.Select(p =>
              {
                  long.TryParse(p, out var id);
                  return id;
              }).ToList(); 
#endif

#if DEBUG
            // Modify Debug to take input from console

            var companyIds = new List<long>();
            if (args.Length == 0)
            {
                Console.WriteLine("Enter space-separated company IDs(press Enter to finish):");

                string input = Console.ReadLine();
                companyIds = input
                    .Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries)
                    .Select(idStr =>
                    {
                        if (long.TryParse(idStr, out var id))
                        {
                            return id;
                        }
                        else
                        {
                            Console.WriteLine($"Invalid input: {idStr}. Please enter valid company IDs.");
                            return 0; // Or handle the error in your preferred way
                        }
                    })
                    .ToList();
            }
            else
            {
                companyIds = args.Select(p =>
                {
                    long.TryParse(p, out var id);
                    return id;
                }).ToList();
            }
#endif

            await app.Process(companyIds);

            Console.WriteLine("");
        }
    }
}