﻿using Microsoft.Azure.WebJobs;
using EmailWebJob.Configuration;
using EmailWebJob.Core;
using Library.EmailService.Interface;
using Library.Infrastructure.Models;

namespace EmailWebJob
{
    public class EmailQueueProcessor
    {
        private EmailProcessor EmailProcessor { get; }
        private readonly bool isDevDeployment = Dependencies.Deployment == "dev";

        public EmailQueueProcessor(EmailProcessor emailProcessor)
        {
            EmailProcessor = emailProcessor;
        }

        public async Task ProcessEmbeddedEmailQueue([QueueTrigger(Dependencies.SendEmbeddedEmailQueue)] GridEvent<EmailMessage> request)
        {
            await EmailProcessor.Process(request.Data, isDevDeployment);
        }

        public async Task ProcessEmbeddedEmailPoisonQueue([QueueTrigger(Dependencies.SendEmbeddedEmailPoisonQueue)] GridEvent<EmailMessage> request)
        {
            await EmailProcessor.Process(request.Data, isDevDeployment);
        }

        public async Task ProcessEmbeddedEmailDeliveryQueue([QueueTrigger(Dependencies.SendEmbeddedEmailDeliveryQueue)] GridEvent<EmailMessage> request)
        {
            await EmailProcessor.Process(request.Data, isDevDeployment);
        }

        public async Task ProcessOTPQueue([QueueTrigger(Dependencies.OTPQueueName)] GridEvent<EmailMessage> request)
        {
            await EmailProcessor.Process(request.Data, isDevDeployment);
        }

        public async Task ProcessEmailQueue([QueueTrigger(Dependencies.SendEmailQueue)] GridEvent<EmailMessage> request)
        {
            await EmailProcessor.Process(request.Data, isDevDeployment);
        }

        public async Task ProcessEmailJockeyQueue([QueueTrigger(Dependencies.SendEmailJockeyQueue)] GridEvent<EmailMessage> request)
        {
            await EmailProcessor.Process(request.Data, isDevDeployment);
        }
    }
}