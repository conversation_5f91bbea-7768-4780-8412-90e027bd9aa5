using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using EmailWebJob.Core;
using Flurl.Http.Configuration;
using Library.EmailService;
using Library.EmailService.Interface;
using Library.EmailService.Model;
using Library.ResilientHttpClient;
using Library.SlackService;
using Serilog;
using Serilog.Events;
using Serilog.Sinks.Grafana.Loki;
using Serilog.Sinks.Slack;

namespace EmailWebJob.Configuration
{
    public static class Dependencies
    {
        public const string SendEmbeddedEmailQueue = "email-embedded-queue";
        public const string SendEmbeddedEmailDeliveryQueue = "email-delivery-status";
        public const string SendEmbeddedEmailPoisonQueue = "email-queue-poison";
        public const string OTPQueueName = "email-queue-otp";
        public const string SendEmailQueue = "email-queue";
        public const string SendEmailJockeyQueue = "email-queue-test";
        public static string Deployment = "dev";

        public static void SetUp(IServiceCollection services, IConfiguration config)
        {
            Deployment = config["AppSettings:Deployment"] ?? "dev";

            #region Connection String Initialized

            var masterStorageConnectionString = config.GetConnectionString("MasterStorageConnectionString");

            #endregion Connection String Initialized

            #region Email Configurations

            var emailOptions = config.GetSection("SMTPEmail").Get<SmtpOptions>() ?? new();

            #endregion Email Configurations

            services
                .AddSingleton<IFlurlClientCache>(_ => new FlurlClientCache())
                .AddScoped(serviceProvider =>
                {
                    var flurlFactory = serviceProvider.GetRequiredService<IFlurlClientCache>();
                    return new ResilientAPIActions(flurlFactory, "https://api.sparkpost.com/api/v1/", "9a7df52c464d7bd804633d89574ffceed36f50f7", "Basic");
                })
                .AddSingleton(Options.Create(emailOptions))
                .AddScoped<IEmailHandler, EmailSparkAPIHandler>()
                .AddSingleton(_ => new ErrorMessenger(masterStorageConnectionString, $"{Deployment}-Email Logger", "#" + config.GetValue<string>("SlackChannelForEmailWebJob") ?? "#default", Library.Infrastructure.QueueService.QueueType.SlackCloudLogs))
                .AddSingleton<EmailProcessor>();
        }

        public static void LogSetUp(LoggerConfiguration loggerConfiguration, IConfiguration config)
        {
            var cred = new LokiCredentials
            {
                Login = config.GetValue<string>("LokiUserName") ?? "reporting",
                Password = config.GetValue<string>("LokiPassword") ?? "bG9raSNmMmtAcmVwb3J0aW5n",
            };
            var labels = Lokilabels.Select(p => new LokiLabel
            {
                Key = p.Key,
                Value = p.Value
            });
            loggerConfiguration
                .Enrich.FromLogContext()
                .WriteTo.Console(LogEventLevel.Warning)
                .WriteTo.GrafanaLoki(
                    config.GetValue<string>("LokiBaseUrl") ?? "https://loki.fieldassist.io/",
                    credentials: cred,
                    labels: labels,
                    restrictedToMinimumLevel: LogEventLevel.Warning,
                    tenant: cred.Login)
                .WriteTo.Slack(
                    webhookUrl: "*********************************************************************************",
                    customChannel: "debug-email",
                    customUsername: $"{Deployment}-emailwebjob",
                    customIcon: ":inbox_tray:",
                    restrictedToMinimumLevel: LogEventLevel.Error
                    );
        }

        private static Dictionary<string, string> Lokilabels = new Dictionary<string, string>
        {
            ["app"] = "emailwebjob",
            ["environment"] = "production",
        };
    }
}