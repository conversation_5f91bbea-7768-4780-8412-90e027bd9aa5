﻿using Libraries.CommonEnums;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace NewDashboardWebjobs.DbStorage.MasterDbModels
{
    public class RoleBasedReportSubscription
    {
        public long Id { get; set; }
        [StringLength(256)]
        public string Name { set; get; }
        public long ReportId { get; set; }
        public long RoleId { get; set; }
        [ForeignKey("Company")]
        public long CompanyId { get; set; }
        public bool IsDeactive { get; set; }
        public ReportSubscriptionType SubscriptionType { get; set; }
        public Guid EncryptionKey { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreationContext { get; set; }
        public virtual Report Report { get; set; }
        public virtual Company Company { get; set; }
        public string Link { get { return $"DataVisualization/Home/ShowReport/{EncryptionKey}"; } }
    }
}
