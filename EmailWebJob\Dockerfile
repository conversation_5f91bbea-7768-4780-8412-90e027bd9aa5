#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["EmailWebJob/EmailWebJob.csproj", "EmailWebJob/"]
COPY ["EmailWebJob.Core/EmailWebJob.Core.csproj", "EmailWebJob.Core/"]
COPY ["FA_Libraries/Libraries.CommonModels/Libraries.CommonModels.csproj", "FA_Libraries/Libraries.CommonModels/"]
COPY ["FA_Libraries/Libraries.CommonEnums/Libraries.CommonEnums.csproj", "FA_Libraries/Libraries.CommonEnums/"]
COPY ["FA_Libraries/Library.EmailService/Library.EmailService.csproj", "FA_Libraries/Library.EmailService/"]
COPY ["FA_Libraries/Library.Infrastructure/Library.Infrastructure.csproj", "FA_Libraries/Library.Infrastructure/"]
COPY ["FA_Libraries/Library.ResilientHttpClient/Library.ResilientHttpClient.csproj", "FA_Libraries/Library.ResilientHttpClient/"]
COPY ["FA_Libraries/Library.SlackService/Library.SlackService.csproj", "FA_Libraries/Library.SlackService/"]
COPY ["FA_Libraries/Library.StorageWriter/Library.StorageWriter.csproj", "FA_Libraries/Library.StorageWriter/"]
COPY ["FA_Libraries/Libraries.Cryptography/Libraries.Cryptography.csproj", "FA_Libraries/Libraries.Cryptography/"]
COPY ["FA_Libraries/Library.StringHelpers/Library.StringHelpers.csproj", "FA_Libraries/Library.StringHelpers/"]
COPY ["FA_Libraries/Library.CommonHelpers/Library.CommonHelpers.csproj", "FA_Libraries/Library.CommonHelpers/"]
RUN dotnet restore "EmailWebJob/EmailWebJob.csproj"
COPY . .
WORKDIR "/src/EmailWebJob"
RUN dotnet build "EmailWebJob.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "EmailWebJob.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "EmailWebJob.dll"]