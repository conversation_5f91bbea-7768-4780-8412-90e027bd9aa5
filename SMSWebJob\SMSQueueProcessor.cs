﻿using Microsoft.Azure.WebJobs;
using Library.Infrastructure.Models;
using Library.SMSHelpers;
using SMSWebjob.Configuration;
using SMSWebJob.Core;

namespace SMSWebjob;

public class SMSQueueProcessor
{
    private SMSProcessor SmsProcessor { get; }
    private readonly bool isDevDeployment = Dependencies.Deployment == "dev";

    public SMSQueueProcessor(SMSProcessor sMSProcessor)
    {
        SmsProcessor = sMSProcessor;
    }

    public async Task ProcessSMSQueue([QueueTrigger(Dependencies.SMSQueue)] GridEvent<SMSMessage> request)
    {
        await SmsProcessor.Process(request.Data, isDevDeployment);
    }
}