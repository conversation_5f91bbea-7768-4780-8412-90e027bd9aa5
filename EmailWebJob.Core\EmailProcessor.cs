﻿using Library.EmailService.Interface;
using Library.FaExceptions;
using Library.SlackService;
using Library.StringHelpers;
using Newtonsoft.Json;
using Serilog;
using Flurl.Http;

namespace EmailWebJob.Core
{
    public class EmailProcessor(IEmailHandler emailHandler, ErrorMessenger errorMessenger)
    {
        private static List<string> invalidDomains = new List<string> { "@alkem.co.in", "@polycab.com", "@fieldfreshfoods.in", "@tops.co.in", "@bonjourgroup.net" };

        #region private methods

        private async Task SendEmail(EmailMessage emailMessage, bool isDevDeployment)
        {
            var debugMessage = emailMessage.Message;
            if (!string.IsNullOrWhiteSpace(emailMessage.ContentPath))
            {
                emailMessage.Message = await Get(emailMessage.ContentPath);
                debugMessage = emailMessage.ContentPath;
            }
            if (isDevDeployment)
            {
                Log.Error("Routed Debug email to: {To}, for subject: {Subject}, Message: {Message}", emailMessage.To, emailMessage.Subject, debugMessage);
                return;
            }
            if (IsEmailInvalid(emailMessage.To))
            {
                var message = $"{emailMessage.To}: Email has been Blocked Due to recent Issue of Non-Delivery";
                await errorMessenger.SendToSlack(message, "", "email_delivery");
                throw new EmailBlockedByFAException(message);
            }
            var response = await emailHandler.SendEmailAsync(emailMessage);
            if (!response.Contains("200"))
            {
                var message = $"Failed To Send Email To {emailMessage.To} For Subject: {emailMessage.Subject} with response: {response}";
                await errorMessenger.SendToSlack(message, "", "email_delivery");
                throw new EmailClientFailedException(response);
            }
        }

        private static bool IsEmailInvalid(string email)
        {
            return string.IsNullOrEmpty(email)
                   || email.Split(",;").Any(s => !StringHelper.IsValidEmail(s))
                   || invalidDomains.Any(e => email.ToLower().Contains(e));
        }

        private async Task<string> Get(string uri)
        {
            try
            {
                return await uri.GetStringAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Unexpected Error while Parsing Data from {uri}:", uri);
                throw;
            }
        }

        #endregion private methods

        public async Task Process(EmailMessage emailMessage, bool isDevDeployment)
        {
            var emailJson = JsonConvert.SerializeObject(emailMessage);
            try
            {
                await SendEmail(emailMessage, isDevDeployment);
                Log.Warning("Sent email to: {To}, for subject: {Subject}", emailMessage.To, emailMessage.Subject);
            }
            catch (EmailBlockedByFAException ex)
            {
                Log.Warning("Blocked Message: {Message} For {To}", ex.Message, emailMessage.To);
            }
            catch (EmailClientFailedException ex)
            {
                Log.Error(ex, "Email Client Responded with Failure Message: {Message} For {To} Subject: {Subject}", ex.Message, emailMessage.To, emailMessage.Subject);
                throw;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Failed Sending Email to: {To}, for subject: {Subject}", emailMessage.To, emailMessage.Subject);
                await errorMessenger.SendToSlack(ex, $"Failed Sending Email to: {emailMessage.To}, for subject: {emailMessage.Subject}");
                throw;
            }
        }
    }
}