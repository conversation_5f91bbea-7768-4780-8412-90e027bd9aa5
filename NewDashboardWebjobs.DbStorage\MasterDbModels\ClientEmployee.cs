﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Libraries.CommonEnums;

namespace NewDashboardWebjobs.DbStorage.MasterDbModels
{
    [Table("ClientEmployees")]
    public class ClientEmployee
    {

        public long? AreaSalesManagerId { get; set; }

        public string AuthKey { get; set; }

        [Column("ClientSideId")]
        public string ErpId { get; set; }

        [Column("Company")]
        public long CompanyId { get; set; }

        public long? CompanyZoneId { get; set; }
        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public DateTime? DateOfJoining { get; set; }

        [Column("IsDeleted")]
        public bool Deleted { get; set; }

        public long? DesignationId { get; set; }

        public string EmailId { get; set; }

        [Column("GUID")]
        public Guid Guid { get; set; }

        public long Id { get; set; }

        public bool IsBillable { set; get; }


        [Column("Deleted")]
        public bool IsDeactive { get; set; }

        public bool IsFieldAppuser { get; set; }

        public bool IsOrderBookingDisabled { get; set; }

        public bool IsTrainingUser { get; set; }

        public bool IsVacant { get; set; }

        public long? KRATagId { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public string LocalName { get; set; }


        public Guid? LoginGuid { get; set; }

        public string Name { get; set; }

        public long? OldTableId { get; set; }

        public ClientEmployee Parent { get; set; }

        public long? ParentId { get; set; }

        [Column("ContactNo")]
        public string PhoneNo { get; set; }


        public long? RegionId { get; set; }

        public PortalUserRole UserRole { get; set; }


        [StringLength(50)]
        public string EmployeeAttributeText1 { get; set; }

        [StringLength(50)]
        public string EmployeeAttributeText2 { get; set; }

        public bool? EmployeeAttributeBoolean1 { get; set; }
        public bool? EmployeeAttributeBoolean2 { get; set; }
        public double? EmployeeAttributeNumber1 { get; set; }
        public double? EmployeeAttributeNumber2 { get; set; }
        public DateTime? EmployeeAttributeDate { get; set; }
    }

}
