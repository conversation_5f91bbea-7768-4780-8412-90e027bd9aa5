#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["SMSWebJob/SMSWebJob.csproj", "SMSWebJob/"]
COPY ["FA_Libraries/Library.ResilientHttpClient/Library.ResilientHttpClient.csproj", "FA_Libraries/Library.ResilientHttpClient/"]
COPY ["FA_Libraries/Libraries.CommonModels/Libraries.CommonModels.csproj", "FA_Libraries/Libraries.CommonModels/"]
COPY ["FA_Libraries/Libraries.CommonEnums/Libraries.CommonEnums.csproj", "FA_Libraries/Libraries.CommonEnums/"]
COPY ["FA_Libraries/Library.SlackService/Library.SlackService.csproj", "FA_Libraries/Library.SlackService/"]
COPY ["FA_Libraries/Library.Infrastructure/Library.Infrastructure.csproj", "FA_Libraries/Library.Infrastructure/"]
COPY ["FA_Libraries/Library.StorageWriter/Library.StorageWriter.csproj", "FA_Libraries/Library.StorageWriter/"]
COPY ["FA_Libraries/Libraries.Cryptography/Libraries.Cryptography.csproj", "FA_Libraries/Libraries.Cryptography/"]
COPY ["SMSWebJob.Core/SMSWebJob.Core.csproj", "SMSWebJob.Core/"]
COPY ["FA_Libraries/Library.EmailService/Library.EmailService.csproj", "FA_Libraries/Library.EmailService/"]
COPY ["FA_Libraries/Library.SMSHelpers/Library.SMSHelpers.csproj", "FA_Libraries/Library.SMSHelpers/"]
RUN dotnet restore "./SMSWebJob/./SMSWebJob.csproj"
COPY . .
WORKDIR "/src/SMSWebJob"
RUN dotnet build "./SMSWebJob.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./SMSWebJob.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "SMSWebJob.dll"]