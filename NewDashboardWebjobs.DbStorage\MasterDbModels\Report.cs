﻿using Libraries.CommonEnums;
using System.ComponentModel.DataAnnotations;

namespace NewDashboardWebjobs.DbStorage.MasterDbModels
{
    public class Report 
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public string Link
        { get { return $"DataVisualization/Home?enumForAssembly={ReportType}"; } }
        public ReportCategory ReportCategory { get; set; }
        public DateTime EmailLocalDeliveryTime { get; set; }
        public ReportFrequency Frequency { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreationContext { get; set; }
        public bool IsDeactive { get; set; }
        public bool CanEmail { set; get; }
        public bool OnlyProductTeamCanAttach { set; get; }
        public EnumForReportAssembly ReportType { get; set; }
        [StringLength(2048)]
        public string Description { get; set; }
        public virtual ICollection<ReportSubscription> ReportSubscriptions { get; set; }
        public long ReportSectionId { get; set; }
        public virtual ReportSection ReportSection { get; set; }
    }

    public class ReportSection
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public ReportSectionHeader ReportSectionEnum { get; set; }
    }

   
}
