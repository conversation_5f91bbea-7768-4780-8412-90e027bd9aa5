﻿using Libraries.CommonEnums;
using Library.StorageWriter.Reader_Writer;
using Library.StringHelpers;
using Microsoft.EntityFrameworkCore;
using NewDashboardWebjobs.DbStorage.DbContexts;
using NewDashboardWebjobs.DbStorage.MasterDbModels;
using Newtonsoft.Json;
using RoleBasedDefaultAccess.Core;
using Serilog;

namespace DefaultRoleAccess.Core
{
    public class RoleBasedDefaultAccessProcessor
    {

        private readonly WritableMasterDbContext _writableDb;
        private readonly MasterDbContext _db;
        private readonly DefaultRoleScreenPermissionsBlobReader blobHandler;
        private static readonly string blobFileName = "permissions.json";

        public RoleBasedDefaultAccessProcessor(WritableMasterDbContext writableDb, 
            DefaultRoleScreenPermissionsBlobReader blobHandler,
            MasterDbContext db)
        {
            _writableDb = writableDb;
            this.blobHandler = blobHandler;
            _db = db;
        }


        #region private functions
        private async Task<bool> CreateRoleBasedReportSubsForCompany(long companyId, long globalAdminRoleId, long companyAdminRoleId, long managerRoleId, long companyExecutiveRoleId)
        {
            try
            {
                var validUserRoles = new List<PortalUserRole>
                                {
                                    PortalUserRole.GlobalAdmin,
                                    PortalUserRole.CompanyExecutive,
                                    PortalUserRole.CompanyAdmin,
                                    PortalUserRole.RegionalAdmin,
                                    PortalUserRole.GlobalSalesManager,
                                    PortalUserRole.NationalSalesManager,
                                    PortalUserRole.ZonalSalesManager,
                                    PortalUserRole.RegionalSalesManager,
                                    PortalUserRole.AreaSalesManager
                                };

                var reportSubscriptions = await _db.ReportSubscriptions
                    .Where(r => r.CompanyId == companyId && !r.IsDeactive && validUserRoles.Contains(r.PortalUserRole))
                    .ToListAsync();

                var managerRoles = new List<PortalUserRole>
                                {
                                    PortalUserRole.GlobalSalesManager,
                                    PortalUserRole.NationalSalesManager,
                                    PortalUserRole.ZonalSalesManager,
                                    PortalUserRole.RegionalSalesManager,
                                    PortalUserRole.AreaSalesManager
                                };

                var managerSubscriptions = reportSubscriptions
                                          .Where(r => managerRoles.Contains(r.PortalUserRole))
                                          .GroupBy(r => r.ReportId)
                                          .Select(group => group.OrderBy(r => (int)r.PortalUserRole).FirstOrDefault());  // picking only the highest manager report entry for creation
             
                var companyAndRegionalAdminSubs  = reportSubscriptions
                                  .Where(r => r.PortalUserRole == PortalUserRole.CompanyAdmin || r.PortalUserRole == PortalUserRole.RegionalAdmin)
                                  .GroupBy(r => r.ReportId)
                                  .Select(group => group.OrderBy(r => (int)r.PortalUserRole).FirstOrDefault()); // first prefernce to company admin entry for creation (as regional admin is same as company admin with positions)

                var otherSubscriptions = reportSubscriptions
                  .Where(r => r.PortalUserRole == PortalUserRole.GlobalAdmin || r.PortalUserRole == PortalUserRole.CompanyExecutive)
                  .Select(sub => sub);

                var combinedSubscriptions = otherSubscriptions.Concat(managerSubscriptions).Concat(companyAndRegionalAdminSubs);

                var roleWiseReportSubsToCreate = new List<RoleBasedReportSubscription>();


                var existingSubscriptions = _db.RoleBasedReportSubscriptions.Where(p => p.CompanyId == companyId && !p.IsDeactive).ToList();

                foreach (var subscription in combinedSubscriptions)
                {
                    var roleId = GetRoleIdForSubscription(subscription.PortalUserRole, globalAdminRoleId, companyAdminRoleId, managerRoleId, companyExecutiveRoleId);

                    var existingSubscription =  existingSubscriptions
                                             .FirstOrDefault(s =>
                                                 s.ReportId == subscription.ReportId &&
                                                 s.RoleId == roleId &&
                                                 s.Name == subscription.Name &&
                                                 s.SubscriptionType == subscription.SubscriptionType);
                    if (existingSubscription == null)
                    {
                        var roleWiseSubscription = new RoleBasedReportSubscription
                        {
                            Name = subscription.Name,
                            ReportId = subscription.ReportId,
                            RoleId = roleId,
                            CompanyId = subscription.CompanyId,
                            IsDeactive = subscription.IsDeactive,
                            SubscriptionType = subscription.SubscriptionType,
                            EncryptionKey = subscription.EncryptionKey,
                            CreatedAt = DateTime.UtcNow,
                            LastUpdatedAt = DateTime.UtcNow,
                            CreationContext = "FA_Webjobs: New Dashboard",
                        };
                        roleWiseReportSubsToCreate.Add(roleWiseSubscription);
                    }
                   
                }
                if (roleWiseReportSubsToCreate.Any())
                {
                    _writableDb.RoleBasedReportSubscriptions.AddRange(roleWiseReportSubsToCreate);
                    await _writableDb.SaveChangesAsync();
                }

                Log.Information("Role wise report subscriptions assigned for companyId : {companyId}", companyId);
                return true;

            }
            catch (Exception ex)
            {
                Log.Error(ex, $"some error occured.Failed to assign role wise report subscriptions for company {companyId}.");
                return false;
            }
        }

        private long GetRoleIdForSubscription(PortalUserRole userRole, long globalAdminRoleId, long companyAdminRoleId, long managerRoleId, long companyExecutiveRoleId)
        {
            switch (userRole)
            {
                case PortalUserRole.GlobalAdmin:
                    return globalAdminRoleId;
                case PortalUserRole.CompanyAdmin:
                case PortalUserRole.RegionalAdmin: 
                    return companyAdminRoleId;
                case PortalUserRole.GlobalSalesManager:
                case PortalUserRole.NationalSalesManager:
                case PortalUserRole.ZonalSalesManager:
                case PortalUserRole.RegionalSalesManager:
                case PortalUserRole.AreaSalesManager:
                    return managerRoleId;
                case PortalUserRole.CompanyExecutive:
                    return companyExecutiveRoleId;
                default:
                    return -1;
            }
        }

        private async Task<bool> UpdateCompanyModuleList(long companyId, PermissionData permissions)
        {
            try
            {

                var companyDefaultModuleList = await _db.Modules.Include(m=>m.ScreenLists).Where(p => p.IsDefault).GroupBy(p => p.Product)
                                        .ToDictionaryAsync(
                                            group => group.Key.ToString(),
                                            group => group.ToDictionary(
                                                module => module.ModuleEnum.ToString(),
                                                module => module.ScreenLists
                                                    .Where(screen => screen.IsDefault)
                                                    .Select(screen => screen.ScreenEnum.ToString())
                                                    .ToList()
                                            )
                                        );

                var company = await _writableDb.Companies.Where(p => p.Id == companyId && !p.Deleted).FirstOrDefaultAsync();
                company.ModuleLists = JsonConvert.SerializeObject(companyDefaultModuleList);
                await _writableDb.SaveChangesAsync();
                Log.Information("Default module list assigned for companyId : {companyId}, ", companyId);
                return true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, $"some error occured.Failed to assign module list for company {companyId}.");
                return false;
            }
        }

        private async Task<bool> AssignUserRoles(long companyId, long globalAdminRoleId, long companyAdminRoleId, long companyExecutiveRoleId, long managerRoleId, long accountManagerRoleId)
        {

            try
            {
                var activeUsers = await _writableDb.IdsLogins.Where(p => p.CompanyId == companyId && !p.IsDeactive).ToListAsync();
                foreach (var user in activeUsers)
                {
                    if (user.UserRole == PortalUserRole.GlobalAdmin)
                    {
                        user.RoleIds = globalAdminRoleId.ToString();
                    }
                    else if (user.UserRole == PortalUserRole.CompanyAdmin || user.UserRole == PortalUserRole.RegionalAdmin)
                    {
                        user.RoleIds = companyAdminRoleId.ToString();
                    }
                    else if (user.UserRole == PortalUserRole.CompanyExecutive)
                    {
                        user.RoleIds = companyExecutiveRoleId.ToString();
                    }
                    else if (user.UserRole == PortalUserRole.AccountManager)
                    {
                        user.RoleIds = accountManagerRoleId.ToString();
                    }
                    else if (user.UserRole == PortalUserRole.AreaSalesManager
                             || user.UserRole == PortalUserRole.RegionalSalesManager
                             || user.UserRole == PortalUserRole.ZonalSalesManager
                             || user.UserRole == PortalUserRole.NationalSalesManager
                             || user.UserRole == PortalUserRole.GlobalSalesManager)
                    {
                        user.RoleIds = managerRoleId.ToString();
                    }
                }
                await _writableDb.SaveChangesAsync();
                Log.Information("Default roles created and assigned for companyId : {companyId}, ", companyId);
                return true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, $"some error occured.Failed to assign default roles for users in company {companyId}.");
                return false;
            }
        }

        private void AssignRoleIdIfNeeded(long? existingRoleId, string roleName, Dictionary<string, Dictionary<string, int>> permissions, Hierarchy hierarchy, List<RoleMaster> roleMastersToCreate, long companyId)
        {
            if (!existingRoleId.HasValue)
            {
                var newRole = new RoleMaster(companyId)
                {
                    RoleName = roleName,
                    RoleDescription = roleName,
                    Permissions = JsonConvert.SerializeObject(permissions),
                    Hierarchy = hierarchy,
                };
                roleMastersToCreate.Add(newRole);
            }
        }

        private async Task<(long, long, long, long)> GetOrCreateRoleIds(long companyId, PermissionData defaultRolePermissions)
        {
            var existingRoles = await _db.RoleMasters.Where(p => p.CompanyId == companyId && !p.IsDeleted).ToListAsync();

            long companyAdminRoleId, managerRoleId, companyExecutiveRoleId, accountManagerRoleId;

            var existingCompanyAdminRoleId = existingRoles
                .FirstOrDefault(p => p.RoleName.NormalizeCaps() == PortalUserRole.CompanyAdmin.ToString().NormalizeCaps())?.Id;

            var existingManagerRoleId = existingRoles
                .FirstOrDefault(p => p.RoleName.NormalizeCaps() == "Manager".NormalizeCaps())?.Id;

            var existingCompanyExeRoleId = existingRoles
                .FirstOrDefault(p => p.RoleName.NormalizeCaps() == PortalUserRole.CompanyExecutive.ToString().NormalizeCaps())?.Id;

            var existingAccountManagerRoleId = existingRoles
               .FirstOrDefault(p => p.RoleName.NormalizeCaps() == PortalUserRole.AccountManager.ToString().NormalizeCaps())?.Id;

            var roleMastersToCreate = new List<RoleMaster>();

            AssignRoleIdIfNeeded(existingCompanyAdminRoleId, PortalUserRole.CompanyAdmin.ToString(), defaultRolePermissions.CompanyAdminPermissionList, Hierarchy.Admin, roleMastersToCreate, companyId);
            AssignRoleIdIfNeeded(existingManagerRoleId, "Manager", defaultRolePermissions.ManagerPermissionList, Hierarchy.Sales, roleMastersToCreate, companyId);
            AssignRoleIdIfNeeded(existingCompanyExeRoleId, PortalUserRole.CompanyExecutive.ToString(), defaultRolePermissions.CompanyExecutivePermissionList, Hierarchy.Admin, roleMastersToCreate, companyId);
            AssignRoleIdIfNeeded(existingAccountManagerRoleId, PortalUserRole.AccountManager.ToString(), defaultRolePermissions.AccountManagerPermissionList, Hierarchy.Admin, roleMastersToCreate, companyId);

            if (roleMastersToCreate.Any())
            {
                _writableDb.RoleMasters.AddRange(roleMastersToCreate);
                await _writableDb.SaveChangesAsync();
            }

            companyAdminRoleId = existingCompanyAdminRoleId ?? roleMastersToCreate.FirstOrDefault(r => r.RoleName == PortalUserRole.CompanyAdmin.ToString())?.Id ?? 0;
            managerRoleId = existingManagerRoleId ?? roleMastersToCreate.FirstOrDefault(r => r.RoleName == "Manager")?.Id ?? 0;
            companyExecutiveRoleId = existingCompanyExeRoleId ?? roleMastersToCreate.FirstOrDefault(r => r.RoleName == PortalUserRole.CompanyExecutive.ToString())?.Id ?? 0;
            accountManagerRoleId = existingAccountManagerRoleId ?? roleMastersToCreate.FirstOrDefault(r => r.RoleName == PortalUserRole.AccountManager.ToString())?.Id ?? 0;

            return (companyAdminRoleId, managerRoleId, companyExecutiveRoleId, accountManagerRoleId);
        }

        #endregion private functions

        public async Task Process(List<long> companyIds)
        {
            try
            {
                if(companyIds.Any())
                {
                   
                    var defaultRolePermissions = await GetPermissionsFromJsonSource(blobFileName);
                    var globalAdminDefaultRole = await _db.RoleMasters.Where(p => p.CompanyId == 1 && !p.IsDeleted && p.RoleName == "GlobalAdmin").FirstOrDefaultAsync();
                    if (globalAdminDefaultRole == null)
                    {
                        Log.Error("GlobalAdmin role not found with Name GlobalAdmin for company Id 1. Terminating the process");
                        return;
                    }
                    var globalAdminRoleId = (globalAdminDefaultRole).Id;

                    foreach (var companyId in companyIds)
                    {
                        Log.Information("Processing companyId : {companyId} ", companyId);
                        var (companyAdminRoleId, managerRoleId, companyExecutiveRoleId, accountManagerRoleId) = await GetOrCreateRoleIds(companyId, defaultRolePermissions);
                        await AssignUserRoles(companyId,globalAdminRoleId, companyAdminRoleId, companyExecutiveRoleId, managerRoleId, accountManagerRoleId);
                        await UpdateCompanyModuleList(companyId, defaultRolePermissions);
                        await CreateRoleBasedReportSubsForCompany(companyId, globalAdminRoleId, companyAdminRoleId, managerRoleId, companyExecutiveRoleId);
                    }

                    Log.Information("Default roles,subscriptions and company module lists assigned successfully for the given input. terminating the process");
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Failed to assign default roles. some error occured.");
            }
        }


        public async Task<PermissionData> GetPermissionsFromJsonSource(string source)
        {
            var dataStr = await blobHandler.ReadBlobContentAsync(source);  // TODO: can replace with GetBlobAsString method if works. GetBlobAsString reader giving error as memory stream is unreadable due to its scope.
            var jsonContent = JsonConvert.DeserializeObject<PermissionData>(dataStr);
            return jsonContent;
        }

    }
}